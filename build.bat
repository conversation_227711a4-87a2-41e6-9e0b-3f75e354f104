@echo off
echo Compilando F1 Launcher...
echo.

REM Verificar si Maven está instalado
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Maven no está instalado o no está en el PATH
    echo Por favor instala Maven para compilar el proyecto
    pause
    exit /b 1
)

echo Limpiando proyecto anterior...
mvn clean

echo Compilando proyecto...
mvn compile

echo Creando JAR ejecutable...
mvn package

if exist "target\F1Launcher.jar" (
    echo.
    echo ¡Compilación exitosa!
    echo JAR creado en: target\F1Launcher.jar
    echo.
    echo Para ejecutar:
    echo   - Usar launch.bat
    echo   - O ejecutar: java -jar target\F1Launcher.jar
) else (
    echo.
    echo Error en la compilación
    echo Revisa los mensajes de error arriba
)

echo.
pause
