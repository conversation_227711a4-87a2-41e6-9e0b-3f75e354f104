package org.example;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Properties;

/**
 * Maneja la configuración de la aplicación F1 Launcher
 */
public class ConfigManager {
    private static final String CONFIG_FILE = "config.properties";
    private static final String APP_DIR = System.getProperty("user.home") + File.separator + ".f1launcher";
    private static final Path CONFIG_PATH = Paths.get(APP_DIR, CONFIG_FILE);
    
    private Properties properties;
    
    // Rutas comunes donde puede estar instalado F1 24
    private static final String[] COMMON_F1_PATHS = {
        "C:\\Program Files (x86)\\Steam\\steamapps\\common\\F1 24\\F1_24.exe",
        "C:\\Program Files\\Steam\\steamapps\\common\\F1 24\\F1_24.exe",
        "C:\\Program Files (x86)\\EA Games\\F1 24\\F1_24.exe",
        "C:\\Program Files\\EA Games\\F1 24\\F1_24.exe",
        "C:\\Games\\F1 24\\F1_24.exe",
        "D:\\Steam\\steamapps\\common\\F1 24\\F1_24.exe",
        "D:\\Games\\F1 24\\F1_24.exe"
    };
    
    public ConfigManager() {
        createAppDirectoryIfNotExists();
        loadConfig();
    }
    
    /**
     * Crea el directorio de la aplicación si no existe
     */
    private void createAppDirectoryIfNotExists() {
        try {
            Path appDirPath = Paths.get(APP_DIR);
            if (!Files.exists(appDirPath)) {
                Files.createDirectories(appDirPath);
            }
        } catch (IOException e) {
            System.err.println("Error creando directorio de aplicación: " + e.getMessage());
        }
    }
    
    /**
     * Carga la configuración desde el archivo
     */
    private void loadConfig() {
        properties = new Properties();
        
        if (Files.exists(CONFIG_PATH)) {
            try (InputStream input = Files.newInputStream(CONFIG_PATH)) {
                properties.load(input);
            } catch (IOException e) {
                System.err.println("Error cargando configuración: " + e.getMessage());
            }
        }
        
        // Si no hay ruta del juego configurada, intentar detectarla automáticamente
        if (getGamePath() == null || getGamePath().isEmpty()) {
            detectGamePath();
        }
        
        // Establecer valores por defecto
        setDefaultValues();
    }
    
    /**
     * Establece valores por defecto para la configuración
     */
    private void setDefaultValues() {
        if (!properties.containsKey("auto_close_time")) {
            properties.setProperty("auto_close_time", "300000"); // 5 minutos en milisegundos
        }
        if (!properties.containsKey("fullscreen")) {
            properties.setProperty("fullscreen", "true");
        }
        saveConfig();
    }
    
    /**
     * Intenta detectar automáticamente la ruta del juego F1 24
     */
    private void detectGamePath() {
        for (String path : COMMON_F1_PATHS) {
            File gameFile = new File(path);
            if (gameFile.exists() && gameFile.isFile()) {
                properties.setProperty("game_path", path);
                saveConfig();
                System.out.println("F1 24 detectado en: " + path);
                return;
            }
        }
        System.out.println("No se pudo detectar automáticamente F1 24. Configurar manualmente.");
    }
    
    /**
     * Guarda la configuración en el archivo
     */
    private void saveConfig() {
        try (OutputStream output = Files.newOutputStream(CONFIG_PATH)) {
            properties.store(output, "F1 Launcher Configuration");
        } catch (IOException e) {
            System.err.println("Error guardando configuración: " + e.getMessage());
        }
    }
    
    /**
     * Obtiene la ruta del juego F1 24
     */
    public String getGamePath() {
        return properties.getProperty("game_path");
    }
    
    /**
     * Establece la ruta del juego F1 24
     */
    public void setGamePath(String path) {
        properties.setProperty("game_path", path);
        saveConfig();
    }
    
    /**
     * Obtiene el tiempo de cierre automático en milisegundos
     */
    public long getAutoCloseTime() {
        return Long.parseLong(properties.getProperty("auto_close_time", "300000"));
    }
    
    /**
     * Establece el tiempo de cierre automático
     */
    public void setAutoCloseTime(long timeMs) {
        properties.setProperty("auto_close_time", String.valueOf(timeMs));
        saveConfig();
    }
    
    /**
     * Verifica si el modo pantalla completa está habilitado
     */
    public boolean isFullscreen() {
        return Boolean.parseBoolean(properties.getProperty("fullscreen", "true"));
    }
    
    /**
     * Establece el modo pantalla completa
     */
    public void setFullscreen(boolean fullscreen) {
        properties.setProperty("fullscreen", String.valueOf(fullscreen));
        saveConfig();
    }
}
