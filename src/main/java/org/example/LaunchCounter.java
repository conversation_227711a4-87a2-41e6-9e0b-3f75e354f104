package org.example;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Maneja el contador de ejecuciones del launcher F1
 */
public class LaunchCounter {
    private static final String COUNTER_FILE = "launch_count.dat";
    private static final String APP_DIR = System.getProperty("user.home") + File.separator + ".f1launcher";
    private static final Path COUNTER_PATH = Paths.get(APP_DIR, COUNTER_FILE);
    
    private int count;
    
    public LaunchCounter() {
        createAppDirectoryIfNotExists();
        loadCount();
    }
    
    /**
     * Crea el directorio de la aplicación si no existe
     */
    private void createAppDirectoryIfNotExists() {
        try {
            Path appDirPath = Paths.get(APP_DIR);
            if (!Files.exists(appDirPath)) {
                Files.createDirectories(appDirPath);
            }
        } catch (IOException e) {
            System.err.println("Error creando directorio de aplicación: " + e.getMessage());
        }
    }
    
    /**
     * Carga el contador desde el archivo
     */
    private void loadCount() {
        try {
            if (Files.exists(COUNTER_PATH)) {
                String content = Files.readString(COUNTER_PATH);
                count = Integer.parseInt(content.trim());
            } else {
                count = 0;
            }
        } catch (IOException | NumberFormatException e) {
            System.err.println("Error cargando contador: " + e.getMessage());
            count = 0;
        }
    }
    
    /**
     * Guarda el contador en el archivo
     */
    private void saveCount() {
        try {
            Files.writeString(COUNTER_PATH, String.valueOf(count));
        } catch (IOException e) {
            System.err.println("Error guardando contador: " + e.getMessage());
        }
    }
    
    /**
     * Incrementa el contador y lo guarda
     */
    public void increment() {
        count++;
        saveCount();
    }
    
    /**
     * Obtiene el valor actual del contador
     */
    public int getCount() {
        return count;
    }
    
    /**
     * Resetea el contador a 0
     */
    public void reset() {
        count = 0;
        saveCount();
    }
}
