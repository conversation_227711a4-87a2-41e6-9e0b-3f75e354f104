package org.example;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * Maneja la ejecución del juego F1 24
 */
public class GameLauncher {
    private final ConfigManager configManager;
    private Process gameProcess;
    
    public GameLauncher(ConfigManager configManager) {
        this.configManager = configManager;
    }
    
    /**
     * Lanza el juego F1 24
     * @return true si el juego se lanzó exitosamente, false en caso contrario
     */
    public boolean launchGame() {
        String gamePath = configManager.getGamePath();
        
        if (gamePath == null || gamePath.isEmpty()) {
            System.err.println("Ruta del juego no configurada");
            return false;
        }
        
        File gameFile = new File(gamePath);
        if (!gameFile.exists()) {
            System.err.println("El archivo del juego no existe: " + gamePath);
            return false;
        }
        
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(gamePath);
            processBuilder.directory(gameFile.getParentFile());
            
            gameProcess = processBuilder.start();
            
            System.out.println("F1 24 lanzado exitosamente");
            return true;
            
        } catch (IOException e) {
            System.err.println("Error lanzando el juego: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Lanza el juego de forma asíncrona y retorna un CompletableFuture
     */
    public CompletableFuture<Boolean> launchGameAsync() {
        return CompletableFuture.supplyAsync(this::launchGame);
    }
    
    /**
     * Verifica si el juego está ejecutándose
     */
    public boolean isGameRunning() {
        return gameProcess != null && gameProcess.isAlive();
    }
    
    /**
     * Termina el proceso del juego si está ejecutándose
     */
    public void terminateGame() {
        if (gameProcess != null && gameProcess.isAlive()) {
            gameProcess.destroy();
            
            // Si no se termina en 5 segundos, forzar terminación
            try {
                if (!gameProcess.waitFor(5, java.util.concurrent.TimeUnit.SECONDS)) {
                    gameProcess.destroyForcibly();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                gameProcess.destroyForcibly();
            }
        }
    }
    
    /**
     * Espera a que el juego termine
     */
    public void waitForGameToFinish() {
        if (gameProcess != null) {
            try {
                gameProcess.waitFor();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * Obtiene el código de salida del juego (solo disponible después de que termine)
     */
    public int getGameExitCode() {
        if (gameProcess != null && !gameProcess.isAlive()) {
            return gameProcess.exitValue();
        }
        return -1;
    }
    
    /**
     * Verifica si la ruta del juego es válida
     */
    public boolean isGamePathValid() {
        String gamePath = configManager.getGamePath();
        if (gamePath == null || gamePath.isEmpty()) {
            return false;
        }
        
        File gameFile = new File(gamePath);
        return gameFile.exists() && gameFile.isFile() && gameFile.canExecute();
    }
}
