package org.example;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import javax.imageio.ImageIO;

/**
 * Aplicación principal F1 Launcher con interfaz gráfica en pantalla completa
 */
public class F1LauncherApp extends JFrame implements KeyListener {
    private final ConfigManager configManager;
    private final LaunchCounter launchCounter;
    private final GameLauncher gameLauncher;
    
    private JButton launchButton;
    private JLabel counterLabel;
    private JLabel statusLabel;
    private Timer autoCloseTimer;
    private BufferedImage backgroundImage;
    
    public F1LauncherApp() {
        this.configManager = new ConfigManager();
        this.launchCounter = new LaunchCounter();
        this.gameLauncher = new GameLauncher(configManager);
        
        initializeUI();
        loadBackgroundImage();
        setupAutoCloseTimer();
    }
    
    /**
     * Inicializa la interfaz de usuario
     */
    private void initializeUI() {
        setTitle("F1 24 Launcher");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setUndecorated(true); // Sin bordes para pantalla completa
        
        // Configurar pantalla completa
        if (configManager.isFullscreen()) {
            setExtendedState(JFrame.MAXIMIZED_BOTH);
            GraphicsEnvironment.getLocalGraphicsEnvironment()
                    .getDefaultScreenDevice()
                    .setFullScreenWindow(this);
        } else {
            setSize(1200, 800);
            setLocationRelativeTo(null);
        }
        
        // Panel principal con imagen de fondo
        JPanel mainPanel = new BackgroundPanel();
        mainPanel.setLayout(new BorderLayout());
        
        // Panel central con componentes
        JPanel centerPanel = createCenterPanel();
        mainPanel.add(centerPanel, BorderLayout.CENTER);
        
        // Panel inferior con información
        JPanel bottomPanel = createBottomPanel();
        mainPanel.add(bottomPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // Agregar listener para teclas
        addKeyListener(this);
        setFocusable(true);
        
        updateCounterDisplay();
        updateStatusDisplay();
    }
    
    /**
     * Crea el panel central con el botón principal
     */
    private JPanel createCenterPanel() {
        JPanel panel = new JPanel();
        panel.setOpaque(false);
        panel.setLayout(new GridBagLayout());
        
        GridBagConstraints gbc = new GridBagConstraints();
        
        // Título
        JLabel titleLabel = new JLabel("F1 24 LAUNCHER");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 48));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.insets = new Insets(0, 0, 50, 0);
        panel.add(titleLabel, gbc);
        
        // Botón de lanzamiento
        launchButton = createLaunchButton();
        gbc.gridy = 1;
        gbc.insets = new Insets(0, 0, 30, 0);
        panel.add(launchButton, gbc);
        
        // Contador de ejecuciones
        counterLabel = new JLabel();
        counterLabel.setFont(new Font("Arial", Font.BOLD, 24));
        counterLabel.setForeground(Color.YELLOW);
        counterLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        gbc.gridy = 2;
        gbc.insets = new Insets(0, 0, 0, 0);
        panel.add(counterLabel, gbc);
        
        return panel;
    }
    
    /**
     * Crea el botón de lanzamiento estilizado
     */
    private JButton createLaunchButton() {
        JButton button = new JButton("LANZAR F1 24");
        button.setFont(new Font("Arial", Font.BOLD, 32));
        button.setPreferredSize(new Dimension(400, 100));
        
        // Estilo del botón
        button.setBackground(new Color(220, 20, 20)); // Rojo F1
        button.setForeground(Color.WHITE);
        button.setBorder(BorderFactory.createRaisedBevelBorder());
        button.setFocusPainted(false);
        
        // Efectos hover
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(255, 50, 50));
            }
            
            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(220, 20, 20));
            }
        });
        
        // Acción del botón
        button.addActionListener(new LaunchButtonListener());
        
        return button;
    }
    
    /**
     * Crea el panel inferior con información de estado
     */
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setOpaque(false);
        panel.setBorder(BorderFactory.createEmptyBorder(10, 20, 20, 20));
        
        statusLabel = new JLabel();
        statusLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        statusLabel.setForeground(Color.WHITE);
        
        JLabel instructionsLabel = new JLabel("Presiona ESC para salir | ENTER para lanzar");
        instructionsLabel.setFont(new Font("Arial", Font.PLAIN, 14));
        instructionsLabel.setForeground(Color.LIGHT_GRAY);
        instructionsLabel.setHorizontalAlignment(SwingConstants.RIGHT);
        
        panel.add(statusLabel, BorderLayout.WEST);
        panel.add(instructionsLabel, BorderLayout.EAST);
        
        return panel;
    }
    
    /**
     * Carga la imagen de fondo
     */
    private void loadBackgroundImage() {
        try {
            // Intentar cargar imagen desde recursos
            InputStream imageStream = getClass().getResourceAsStream("/f1-background.jpg");
            if (imageStream != null) {
                backgroundImage = ImageIO.read(imageStream);
            } else {
                // Crear imagen de fondo por defecto si no se encuentra
                createDefaultBackground();
            }
        } catch (IOException e) {
            System.err.println("Error cargando imagen de fondo: " + e.getMessage());
            createDefaultBackground();
        }
    }
    
    /**
     * Crea una imagen de fondo por defecto
     */
    private void createDefaultBackground() {
        backgroundImage = new BufferedImage(1920, 1080, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = backgroundImage.createGraphics();
        
        // Gradiente de fondo
        GradientPaint gradient = new GradientPaint(
                0, 0, new Color(20, 20, 40),
                0, 1080, new Color(60, 60, 100)
        );
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, 1920, 1080);
        
        // Texto F1 de fondo
        g2d.setColor(new Color(255, 255, 255, 30));
        g2d.setFont(new Font("Arial", Font.BOLD, 200));
        FontMetrics fm = g2d.getFontMetrics();
        String text = "F1";
        int x = (1920 - fm.stringWidth(text)) / 2;
        int y = (1080 + fm.getAscent()) / 2;
        g2d.drawString(text, x, y);
        
        g2d.dispose();
    }

    /**
     * Configura el timer de cierre automático
     */
    private void setupAutoCloseTimer() {
        long autoCloseTime = configManager.getAutoCloseTime();
        autoCloseTimer = new Timer((int) autoCloseTime, e -> {
            System.out.println("Cerrando aplicación automáticamente...");
            System.exit(0);
        });
        autoCloseTimer.setRepeats(false);
        autoCloseTimer.start();
    }

    /**
     * Actualiza la visualización del contador
     */
    private void updateCounterDisplay() {
        int count = launchCounter.getCount();
        counterLabel.setText("Ejecuciones: " + count);
    }

    /**
     * Actualiza la visualización del estado
     */
    private void updateStatusDisplay() {
        if (gameLauncher.isGamePathValid()) {
            statusLabel.setText("✓ F1 24 detectado - Listo para lanzar");
            statusLabel.setForeground(Color.GREEN);
            launchButton.setEnabled(true);
        } else {
            statusLabel.setText("⚠ F1 24 no encontrado - Verificar instalación");
            statusLabel.setForeground(Color.ORANGE);
            launchButton.setEnabled(false);
        }
    }

    /**
     * Listener para el botón de lanzamiento
     */
    private class LaunchButtonListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            launchGame();
        }
    }

    /**
     * Lanza el juego F1 24
     */
    private void launchGame() {
        launchButton.setEnabled(false);
        statusLabel.setText("Lanzando F1 24...");
        statusLabel.setForeground(Color.YELLOW);

        // Incrementar contador
        launchCounter.increment();
        updateCounterDisplay();

        // Lanzar juego de forma asíncrona
        gameLauncher.launchGameAsync().thenAccept(success -> {
            SwingUtilities.invokeLater(() -> {
                if (success) {
                    statusLabel.setText("✓ F1 24 lanzado exitosamente");
                    statusLabel.setForeground(Color.GREEN);

                    // Cerrar launcher después de un breve delay
                    Timer closeTimer = new Timer(3000, closeEvent -> {
                        System.out.println("Cerrando launcher...");
                        System.exit(0);
                    });
                    closeTimer.setRepeats(false);
                    closeTimer.start();
                } else {
                    statusLabel.setText("✗ Error lanzando F1 24");
                    statusLabel.setForeground(Color.RED);
                    launchButton.setEnabled(true);
                }
            });
        });
    }

    /**
     * Panel personalizado con imagen de fondo
     */
    private class BackgroundPanel extends JPanel {
        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);

            if (backgroundImage != null) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                                   RenderingHints.VALUE_INTERPOLATION_BILINEAR);

                // Escalar imagen para ajustar al panel
                int panelWidth = getWidth();
                int panelHeight = getHeight();

                g2d.drawImage(backgroundImage, 0, 0, panelWidth, panelHeight, null);
                g2d.dispose();
            }
        }
    }

    // Implementación de KeyListener
    @Override
    public void keyPressed(KeyEvent e) {
        switch (e.getKeyCode()) {
            case KeyEvent.VK_ESCAPE:
                System.exit(0);
                break;
            case KeyEvent.VK_ENTER:
                if (launchButton.isEnabled()) {
                    launchGame();
                }
                break;
            case KeyEvent.VK_F11:
                // Toggle pantalla completa
                boolean isFullscreen = configManager.isFullscreen();
                configManager.setFullscreen(!isFullscreen);
                dispose();
                new F1LauncherApp().setVisible(true);
                break;
        }
    }

    @Override
    public void keyTyped(KeyEvent e) {
        // No implementado
    }

    @Override
    public void keyReleased(KeyEvent e) {
        // No implementado
    }

    /**
     * Método principal para lanzar la aplicación
     */
    public static void main(String[] args) {
        // Configurar Look and Feel del sistema
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            System.err.println("Error configurando Look and Feel: " + e.getMessage());
        }

        // Lanzar aplicación en el hilo de eventos de Swing
        SwingUtilities.invokeLater(() -> {
            new F1LauncherApp().setVisible(true);
        });
    }
}
