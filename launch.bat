@echo off
echo Iniciando F1 Launcher...
echo.

REM Verificar si Java está instalado
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java no está instalado o no está en el PATH
    echo Por favor instala Java 24 o superior
    pause
    exit /b 1
)

REM Verificar si Maven está instalado
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Maven no está instalado o no está en el PATH
    echo Intentando ejecutar con Java directamente...
    
    REM Buscar JAR compilado
    if exist "target\F1Launcher.jar" (
        echo Ejecutando JAR precompilado...
        java -jar target\F1Launcher.jar
    ) else (
        echo Error: No se encontró JAR compilado
        echo Por favor compila el proyecto primero con: mvn clean package
        pause
        exit /b 1
    )
) else (
    echo Compilando y ejecutando F1 Launcher...
    mvn clean compile exec:java -Dexec.mainClass="org.example.Main"
)

pause
