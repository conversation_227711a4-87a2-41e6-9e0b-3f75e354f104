# F1 Launcher

Una aplicación Java en pantalla completa para lanzar F1 24 con contador de ejecuciones y interfaz personalizada.

## Características

- **Pantalla completa**: Interfaz inmersiva sin bordes
- **Imagen de fondo personalizable**: Soporte para imágenes de F1
- **Contador de ejecuciones**: Rastrea cuántas veces has lanzado el juego
- **Detección automática**: Encuentra automáticamente F1 24 en ubicaciones comunes
- **Cierre automático**: Se cierra automáticamente después de lanzar el juego
- **Controles de teclado**: ESC para salir, ENTER para lanzar, F11 para toggle pantalla completa

## Instalación y Uso

### Prerrequisitos
- Java 24 o superior
- Maven
- F1 24 instalado en tu PC

### Compilar y ejecutar

```bash
# Compilar el proyecto
mvn clean compile

# Ejecutar la aplicación
mvn exec:java -Dexec.mainClass="org.example.Main"
```

### Crear JAR ejecutable

```bash
# Crear JAR con dependencias
mvn clean package

# Ejecutar JAR
java -jar target/F1Launcher-1.0-SNAPSHOT.jar
```

## Configuración

### Detección automática de F1 24
La aplicación intentará detectar automáticamente F1 24 en las siguientes ubicaciones:
- `C:\Program Files (x86)\Steam\steamapps\common\F1 24\F1_24.exe`
- `C:\Program Files\Steam\steamapps\common\F1 24\F1_24.exe`
- `C:\Program Files (x86)\EA Games\F1 24\F1_24.exe`
- `C:\Program Files\EA Games\F1 24\F1_24.exe`
- `C:\Games\F1 24\F1_24.exe`
- `D:\Steam\steamapps\common\F1 24\F1_24.exe`
- `D:\Games\F1 24\F1_24.exe`

### Configuración manual
Si F1 24 no se detecta automáticamente, puedes configurar la ruta manualmente editando:
`%USERPROFILE%\.f1launcher\config.properties`

```properties
game_path=C:\tu\ruta\personalizada\F1_24.exe
auto_close_time=300000
fullscreen=true
```

### Imagen de fondo personalizada
Coloca tu imagen de fondo en: `src/main/resources/f1-background.jpg`

Especificaciones recomendadas:
- Formato: JPG o PNG
- Resolución: 1920x1080 o superior

## Controles

- **Click en botón** o **ENTER**: Lanzar F1 24
- **ESC**: Salir de la aplicación
- **F11**: Alternar modo pantalla completa

## Archivos de datos

La aplicación guarda sus datos en: `%USERPROFILE%\.f1launcher\`
- `config.properties`: Configuración de la aplicación
- `launch_count.dat`: Contador de ejecuciones

## Estructura del proyecto

```
F1Launcher/
├── src/main/java/org/example/
│   ├── Main.java                 # Punto de entrada
│   ├── F1LauncherApp.java       # Aplicación principal con UI
│   ├── GameLauncher.java        # Manejo de ejecución del juego
│   ├── LaunchCounter.java       # Sistema de contador
│   └── ConfigManager.java       # Manejo de configuración
├── src/main/resources/
│   └── f1-background.jpg        # Imagen de fondo (opcional)
└── pom.xml                      # Configuración Maven
```

## Solución de problemas

### F1 24 no se detecta
1. Verifica que F1 24 esté instalado
2. Configura manualmente la ruta en `config.properties`
3. Asegúrate de que el archivo ejecutable tenga permisos

### La aplicación no se cierra automáticamente
- Verifica la configuración `auto_close_time` en `config.properties`
- El valor está en milisegundos (300000 = 5 minutos)

### Problemas de pantalla completa
- Presiona F11 para alternar modo pantalla completa
- Modifica `fullscreen=false` en `config.properties` para modo ventana

## Personalización

### Cambiar tiempo de cierre automático
Edita `auto_close_time` en `config.properties` (en milisegundos):
- 60000 = 1 minuto
- 300000 = 5 minutos
- 600000 = 10 minutos

### Deshabilitar pantalla completa
Cambia `fullscreen=false` en `config.properties`

## Desarrollo

Para contribuir al proyecto:
1. Fork el repositorio
2. Crea una rama para tu feature
3. Realiza tus cambios
4. Envía un pull request

## Licencia

Este proyecto es de código abierto. Úsalo y modifícalo libremente.
